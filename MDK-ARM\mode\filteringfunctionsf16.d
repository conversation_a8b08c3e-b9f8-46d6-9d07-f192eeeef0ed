./mode/filteringfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\FilteringFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_init_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_init_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_levinson_durbin_f16.c
