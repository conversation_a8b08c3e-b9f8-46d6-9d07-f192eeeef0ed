./mode/transformfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\TransformFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_bitreversal.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_bitreversal2.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_const_structs.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix8_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_init_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_q31.c
