./mode/fastmathfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\FastMathFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sqrt_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sqrt_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_divide_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_divide_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_q15.c
