./mode/quaternionmathfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\QuaternionMathFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_norm_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_inverse_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_conjugate_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_normalize_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_product_single_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_product_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion2rotation_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_rotation2quaternion_f32.c
