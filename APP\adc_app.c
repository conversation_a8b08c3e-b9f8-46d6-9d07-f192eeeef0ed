#include "adc_app.h"


uint32_t adc_val_buffer[BUFFER_SIZE];
uint8_t AdcConvEnd = 0;

void adc_tim_dma_init(void)
{
    HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}
// ADC ת����ɻص�
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    UNUSED(hadc);
    if (hadc == &hadc1) // ȷ���� hadc1 ���
    {
        HAL_ADC_Stop_DMA(hadc); // ֹͣ DMA���ȴ�����
        AdcConvEnd = 1;         // ���ñ�־λ
    }
}

void calculate_fft(
    volatile const uint32_t* adc_val_buffer,
    float32_t* fft_output_buf,
    uint32_t fft_length)
{
    arm_cfft_radix4_instance_f32 scfft;
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    // --- Static buffers to avoid stack overflow with large FFT_LENGTH ---
    // These buffers are allocated only once.
    static float32_t signal_float[FFT_LENGTH];
    static float32_t hanning_window[FFT_LENGTH];
    static float32_t signal_windowed[FFT_LENGTH];
    // FFT input buffer requires 2*FFT_LENGTH because it holds complex numbers (real, imag)
    static float32_t fft_input_buf[FFT_LENGTH * 2];

    // 1. Convert raw ADC integer values to normalized float voltage values
    // This part normalizes 12-bit ADC values (0-4095) to a 0-3.3V range.
    for (int i = 0; i < fft_length; i++)
    {
        signal_float[i] = (float32_t)adc_val_buffer[i] / 4096.0f * 3.3f;
    }

    // 2. Generate a Hanning window
    // A window function is applied to reduce spectral leakage.
    arm_hanning_f32(hanning_window, fft_length);

    // 3. Apply the window to the signal (element-wise multiplication)
    arm_mult_f32(signal_float, hanning_window, signal_windowed, fft_length);

    // 4. Prepare the input buffer for the CFFT function
    // The FFT function requires a complex input (real, imaginary, real, imaginary, ...).
    // We set the real part to our windowed signal and the imaginary part to zero.
    for (int i = 0; i < fft_length; i++)
    {
        fft_input_buf[2 * i]     = signal_windowed[i]; // Real part
        fft_input_buf[2 * i + 1] = 0.0f;                // Imaginary part
    }

    // 5. Perform the CFFT (Complex Fast Fourier Transform)
    // The calculation is done in-place, meaning fft_input_buf is overwritten with the result.
    arm_cfft_radix4_f32(&scfft, fft_input_buf);

    // 6. Calculate the magnitude of the complex FFT output
    // The output is now |C|, where C = R + jI. The magnitude is sqrt(R^2 + I^2).
    // The result is stored in the user-provided output buffer.
    arm_cmplx_mag_f32(fft_input_buf, fft_output_buf, fft_length);
}

void concentrate_energy(float *fft_data, int length, int window_size)
{
    static float temp_buffer[4096]; // ��ʱ������

    // ����ԭʼ����
    for (int i = 0; i < length; i++) {
        temp_buffer[i] = fft_data[i];
    }

    // ��ÿ��Ƶ����������ۼ������˱߽磩
    for (int i = window_size/2; i < length - window_size/2; i++) {
        // ����Ƿ��Ǿֲ����ֵ
        int is_local_max = 1; // ʹ��int���bool
        float max_value = temp_buffer[i];

        for (int j = i - window_size/2; j <= i + window_size/2; j++) {
            if (j != i && temp_buffer[j] > max_value) {
                is_local_max = 0; // ʹ��0���false
                break;
            }
        }

        // ����Ǿֲ����ֵ���ۼ���Χ����
        if (is_local_max) {
            float total_energy = 0.0f;
            for (int j = i - window_size/2; j <= i + window_size/2; j++) {
                total_energy += temp_buffer[j];
                if (j != i) {
                    fft_data[j] = 0.0f; // �����Χ�������
                }
            }
            fft_data[i] = total_energy; // ���������ۼ������ĵ�
        }
    }
}

float FFT_OutputBuf[FFT_LENGTH];
float FFT_InputBuf[FFT_LENGTH];

void adc_task(void)
{
    if (AdcConvEnd)
    {
        for (uint16_t i = 0; i < BUFFER_SIZE; i++)
        {
           // my_printf(&huart1, "%d\r\n", (int)adc_val_buffer[i]);
            //HMI_Write_Wave_Low("s0",0,(int)((float)adc_val_buffer[i]/4096.0f*255.0f*3.0f));
            FFT_InputBuf[i]=adc_val_buffer[i]/4096*3.3f;
        }
         // ��ʼ��scfft�ṹ��,�趨FFT����
       calculate_fft(adc_val_buffer,FFT_OutputBuf,FFT_LENGTH);
       //concentrate_energy(FFT_OutputBuf,FFT_LENGTH,10);
        /* for (int i = 0; i < FFT_LENGTH/2; i++)
        {
            my_printf(&huart1,"%.2f\r\n",FFT_OutputBuf[i]);

        }*/
       /* switch(wave)
        {
            case 0:

            break;

            case 1:
                Analysis_AM();
            break;

            case 2:
                Analysis_FM();
            break;
        }*/
         /*
        wave_data.vpp=Get_Waveform_Vpp(dac_val_buffer, & wave_data.mean, & wave_data.rms);
        my_printf(&huart1, "Vpp: %.2f V, average: %.2f V, valid: %.2f V\r\n", wave_data.vpp, wave_data.mean, wave_data.rms);
        // ��������˲��η�����־������в�
        {�η���*/
      /*  if (wave_analysis_flag)
            wave_data = Get_Waveform_Info(dac_val_buffer);

            // ���ݲ�ѯ���ʹ�ӡ��Ӧ����Ϣ
            switch (wave_query_type)
            {
            case 4: // ��ӡȫ����Ϣ
                my_printf(&huart1, "%d:%.2f %s\r\n",dac_app_get_update_frequency() / WAVEFORM_SAMPLES,
                          (uint32_t)wave_data.frequency,
                          wave_data.vpp,
                          GetWaveformTypeString(wave_data.waveform_type));
                break;

            case 1: // ����ӡ��������
                my_printf(&huart1, "%s\r\n", GetWaveformTypeString(wave_data.waveform_type));
                break;

            case 2: // ����ӡƵ��
                my_printf(&huart1, "%dHz\r\n", (uint32_t)wave_data.frequency);
                break;

            case 3: // ����ӡ���ֵ
                my_printf(&huart1, "%.2fmV\r\n", wave_data.vpp);
                break;
            }

            wave_analysis_flag = 0; // ������ɺ������־
            wave_query_type = 0;    // ���ò�ѯ����
        }
*/
        // --- ������� ---

        // ��մ��������� (��ѡ��ȡ���ں����߼�)
        // memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

        // ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
        // memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)
        AdcConvEnd = 0;

        // �������� ADC DMA ������һ�βɼ�
        // ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
        // ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
       /*HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // �ٴν��ð봫���ж�*/
    }
}

/**
 * @brief 定时器周期回调函数
 * @param htim 定时器句柄
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM3)
    {
        // TIM3定时器中断，调用AD9959处理函数
        // AD9959_proc函数内部已经处理了扫频模式的检查
        AD9959_proc();
    }
}