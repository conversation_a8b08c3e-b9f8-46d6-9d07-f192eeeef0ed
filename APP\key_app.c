#include "key_app.h"



uint8_t key_val,key_old,key_down,key_up;


uint8_t key_read()
{
	uint8_t temp = 0;

	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15) == GPIO_PIN_RESET) temp = 1;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13) == GPIO_PIN_RESET) temp = 2;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11) == GPIO_PIN_RESET) temp = 3;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9) == GPIO_PIN_RESET) temp = 4;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7) == GPIO_PIN_RESET) temp = 5;
	if(HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0) == GPIO_PIN_RESET) temp = 6;

	return temp;
}

uint8_t data=0;
void key_task()
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = ~key_val & (key_old ^ key_val);
	key_old = key_val;

    switch(key_down)
    {

        case 1:
            AD9959_Init();
            AD9959_Single_Output_mV(0, 1950000, 0, 500);
            AD9959_IO_UpDate();
        break;

        case 2:
            AD9959_Init();
             AD9959_Single_Output_mV(0, 49998, 0, 300);
            AD9959_IO_UpDate();

        break;

        case 3:
             HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);

        break;

        case 4:
            // 扫频设置：100Hz到3kHz，步进100Hz，5秒完成
            // 参数：通道0，起始100Hz，结束3000Hz，步进时间172413us，步进频率100Hz
            AD9959_Enable_Sweep_Mode();  // 启用扫频模式，禁止定时器中断中的固定频率输出
            AD9959_Init();  // 初始化AD9959
            AD9959__Sweep_Fre(0, 100, 3000, 172413, 172413, 100, 100, 0);
            AD9959__Sweep_Trigger(0);  // 触发扫频
            AD9959_IO_UpDate();  // 更新寄存器
        break;

        case 5:
            // 禁用扫频模式，恢复正常的固定频率输出
            AD9959_Disable_Sweep_Mode();
            ucled[4]^=1;
        break;

        case 6:
            ucled[5] ^= 1;
        break;
    }


}