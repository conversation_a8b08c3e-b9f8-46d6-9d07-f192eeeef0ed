% --- 1. 读取数据 ---
filename = 'data.log';
fid = fopen(filename, 'r');
if fid == -1
    error('文件无法打开，请检查data.log是否存在于当前路径');
end
data = fscanf(fid, '%f');
fclose(fid);

% --- 2. 应用窗函数 ---
N = length(data); % 获取数据点总数
w = hann(N);      % 创建汉宁窗 (您可以换成 hamming(N) 或 blackman(N) 来对比)
data_windowed = data .* w; % 应用窗函数

% --- 3. 绘制时域图对比 (您已有的部分) ---
figure(1); % 创建第一个图形窗口
subplot(2, 1, 1);
plot(data, 'b-o');
title('原始数据 (未加窗)');
xlabel('样本点 (Index)');
ylabel('幅值 (Value)');
grid on;
axis tight;

subplot(2, 1, 2);
plot(data_windowed, 'r-o');
title('加窗后数据 (使用汉宁窗)');
xlabel('样本点 (Index)');
ylabel('幅值 (Value)');
grid on;
axis tight;

% --- 4. 计算并绘制频谱图对比 (关键步骤) ---
% 计算FFT
fft_original = fft(data);
fft_windowed = fft(data_windowed);

% 计算频率轴
Fs = 1000; % 假设采样频率为1000 Hz，请根据您的实际情况修改
f = (0:N-1)*(Fs/N);

% 计算幅值 (取绝对值并归一化)
magnitude_original = abs(fft_original)/N;
magnitude_windowed = abs(fft_windowed)/N;

% 绘制频谱图
figure(2); % 创建第二个图形窗口
plot(f, 20*log10(magnitude_original), 'b'); % 用分贝(dB)显示，更容易观察
hold on; % 在同一张图上继续绘制
plot(f, 20*log10(magnitude_windowed), 'r', 'LineWidth', 1.5); % 加窗后的频谱用红色粗线
hold off;

title('频谱对比');
xlabel('频率 (Hz)');
ylabel('幅值 (dB)');
legend('未加窗 (矩形窗)', '加窗后 (汉宁窗)');
grid on;
xlim([0 Fs/2]); % 通常只关心到奈奎斯特频率
ylim([-120, max(20*log10(magnitude_windowed)) + 10]); % 调整Y轴范围以便观察

