--cpu=Cortex-M4.fp.sp
".\mode\startup_stm32f429xx.o"
".\mode\main.o"
".\mode\gpio.o"
".\mode\adc.o"
".\mode\dac.o"
".\mode\dma.o"
".\mode\tim.o"
".\mode\usart.o"
".\mode\stm32f4xx_it.o"
".\mode\stm32f4xx_hal_msp.o"
".\mode\stm32f4xx_hal_adc.o"
".\mode\stm32f4xx_hal_adc_ex.o"
".\mode\stm32f4xx_ll_adc.o"
".\mode\stm32f4xx_hal_rcc.o"
".\mode\stm32f4xx_hal_rcc_ex.o"
".\mode\stm32f4xx_hal_flash.o"
".\mode\stm32f4xx_hal_flash_ex.o"
".\mode\stm32f4xx_hal_flash_ramfunc.o"
".\mode\stm32f4xx_hal_gpio.o"
".\mode\stm32f4xx_hal_dma_ex.o"
".\mode\stm32f4xx_hal_dma.o"
".\mode\stm32f4xx_hal_pwr.o"
".\mode\stm32f4xx_hal_pwr_ex.o"
".\mode\stm32f4xx_hal_cortex.o"
".\mode\stm32f4xx_hal.o"
".\mode\stm32f4xx_hal_exti.o"
".\mode\stm32f4xx_hal_dac.o"
".\mode\stm32f4xx_hal_dac_ex.o"
".\mode\stm32f4xx_hal_tim.o"
".\mode\stm32f4xx_hal_tim_ex.o"
".\mode\stm32f4xx_hal_uart.o"
".\mode\system_stm32f4xx.o"
".\mode\scheduler.o"
".\mode\usart_app.o"
".\mode\led_app.o"
".\mode\key_app.o"
".\mode\adc_app.o"
".\mode\ringbuffer.o"
".\mode\ad9959.o"
".\mode\hmi.o"
".\mode\fft.o"
".\mode\basicmathfunctions.o"
".\mode\basicmathfunctionsf16.o"
".\mode\bayesfunctions.o"
".\mode\bayesfunctionsf16.o"
".\mode\commontables.o"
".\mode\commontablesf16.o"
".\mode\complexmathfunctions.o"
".\mode\complexmathfunctionsf16.o"
".\mode\controllerfunctions.o"
".\mode\distancefunctions.o"
".\mode\distancefunctionsf16.o"
".\mode\fastmathfunctions.o"
".\mode\fastmathfunctionsf16.o"
".\mode\filteringfunctions.o"
".\mode\filteringfunctionsf16.o"
".\mode\interpolationfunctions.o"
".\mode\interpolationfunctionsf16.o"
".\mode\matrixfunctions.o"
".\mode\matrixfunctionsf16.o"
".\mode\quaternionmathfunctions.o"
".\mode\svmfunctions.o"
".\mode\svmfunctionsf16.o"
".\mode\statisticsfunctions.o"
".\mode\statisticsfunctionsf16.o"
".\mode\supportfunctions.o"
".\mode\supportfunctionsf16.o"
".\mode\transformfunctions.o"
".\mode\transformfunctionsf16.o"
".\mode\windowfunctions.o"
--strict --scatter ".\mode\mode.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\mode\mode.map" -o .\mode\mode.axf